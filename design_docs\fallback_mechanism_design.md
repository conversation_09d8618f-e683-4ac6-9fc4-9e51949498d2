# Fallback Mechanism Design

## Overview
Design for a robust fallback mechanism that gracefully degrades from local Ollama models to external APIs (OpenAI, Azure Speech, Elevenlabs) when local models are unavailable or fail. The system ensures seamless user experience with minimal disruption during transitions.

## Architecture

### Core Components

#### 1. Fallback Controller
```python
class FallbackController:
    """
    Central controller managing fallback decisions and transitions.
    Monitors service health and orchestrates fallback sequences.
    """
```

#### 2. Service Health Monitor
```python
class ServiceHealthMonitor:
    """
    Monitors health of all available services (local and external).
    Provides real-time status and triggers fallback decisions.
    """
```

#### 3. Transition Manager
```python
class TransitionManager:
    """
    Manages smooth transitions between services.
    Ensures continuity of service during fallback events.
    """
```

#### 4. Configuration Manager
```python
class ConfigurationManager:
    """
    Manages service configurations and credentials.
    Handles dynamic configuration updates during fallbacks.
    """
```

## Fallback Hierarchy

### Service Priority Chain
1. **Primary**: Local Ollama (gemma3:1b)
2. **Secondary**: Alternative Ollama models (gemma:2b, llama3.2:1b)
3. **Tertiary**: External APIs (OpenAI, Azure Speech, Elevenlabs)
4. **Emergency**: Basic local processing (if available)

### Fallback Triggers

#### Automatic Triggers
- **Connection Failure**: Ollama server unreachable
- **Model Failure**: Model loading or inference errors
- **Performance Degradation**: Response times exceed thresholds
- **Resource Exhaustion**: Insufficient system resources
- **Quality Issues**: Poor response quality detected

#### Manual Triggers
- **User Override**: Manual service selection
- **Configuration Change**: Runtime configuration updates
- **Maintenance Mode**: Planned service maintenance
- **Testing**: Fallback testing and validation

## Fallback Decision Logic

### Decision Matrix
```python
FALLBACK_DECISION_MATRIX = {
    "ollama_unavailable": {
        "action": "fallback_to_external",
        "target": "openai",
        "retry_interval": 60  # seconds
    },
    "model_loading_failed": {
        "action": "try_alternative_model",
        "target": "gemma:2b",
        "max_attempts": 3
    },
    "performance_degraded": {
        "action": "switch_service",
        "target": "best_available",
        "threshold": 5.0  # seconds
    },
    "quality_issues": {
        "action": "fallback_to_external",
        "target": "openai",
        "validation_required": True
    }
}
```

### Health Check Criteria
```python
HEALTH_CRITERIA = {
    "ollama": {
        "max_response_time": 5.0,
        "min_success_rate": 0.95,
        "max_error_rate": 0.05,
        "connectivity_timeout": 10.0
    },
    "openai": {
        "max_response_time": 10.0,
        "min_success_rate": 0.98,
        "api_key_required": True,
        "rate_limit_aware": True
    },
    "azure": {
        "max_response_time": 8.0,
        "min_success_rate": 0.97,
        "credentials_required": True,
        "region_aware": True
    }
}
```

## Transition Management

### Graceful Transition Process
1. **Pre-Transition Validation**: Verify target service availability
2. **Current Request Completion**: Allow ongoing requests to complete
3. **Service Switch**: Activate new service configuration
4. **Post-Transition Validation**: Verify successful transition
5. **User Notification**: Inform user of service change (if configured)

### Transition Types

#### Hot Transition
- Immediate switch for critical failures
- Minimal service interruption
- Used for connection failures and errors

#### Warm Transition
- Gradual switch with overlap period
- Allows current operations to complete
- Used for performance degradation

#### Cold Transition
- Planned switch with preparation time
- Full service restart if necessary
- Used for maintenance and updates

## Service Abstraction

### Unified Interface
```python
class ServiceInterface:
    """
    Abstract interface for all services (local and external).
    Provides consistent API regardless of underlying service.
    """
    
    def chat_completion(self, messages: List[Dict], **kwargs) -> Dict:
        pass
    
    def is_healthy(self) -> bool:
        pass
    
    def get_status(self) -> ServiceStatus:
        pass
    
    def configure(self, config: Dict) -> bool:
        pass
```

### Service Implementations
- **OllamaService**: Local Ollama model interface
- **OpenAIService**: OpenAI API interface
- **AzureService**: Azure Speech API interface
- **ElevenlabsService**: Elevenlabs API interface

## Configuration Management

### Dynamic Configuration
```python
FALLBACK_CONFIG = {
    "enabled": True,
    "auto_fallback": True,
    "fallback_chain": [
        "ollama_gemma3_1b",
        "ollama_gemma_2b", 
        "openai_gpt35",
        "azure_speech"
    ],
    "health_check_interval": 30,
    "retry_intervals": [10, 30, 60, 300],  # seconds
    "max_fallback_depth": 3,
    "user_notification": True,
    "logging_level": "INFO"
}
```

### Service Credentials
```python
SERVICE_CREDENTIALS = {
    "openai": {
        "api_key": "env:OPENAI_API_KEY",
        "model": "gpt-3.5-turbo",
        "max_tokens": 1000
    },
    "azure": {
        "api_key": "env:AZURE_SPEECH_KEY",
        "region": "env:AZURE_SPEECH_REGION",
        "voice": "en-US-SoniaNeural"
    },
    "elevenlabs": {
        "api_key": "env:ELEVENLABS_API_KEY",
        "model": "eleven_monolingual_v1"
    }
}
```

## Error Handling

### Error Classification
- **Transient Errors**: Network timeouts, temporary unavailability
- **Persistent Errors**: Service configuration issues, authentication failures
- **Critical Errors**: Complete service failure, data corruption
- **Performance Errors**: Slow responses, quality degradation

### Recovery Strategies
```python
RECOVERY_STRATEGIES = {
    "transient": {
        "action": "retry_with_backoff",
        "max_retries": 3,
        "backoff_factor": 2.0
    },
    "persistent": {
        "action": "fallback_to_next",
        "notify_user": True,
        "log_level": "WARNING"
    },
    "critical": {
        "action": "emergency_fallback",
        "notify_admin": True,
        "log_level": "ERROR"
    }
}
```

## User Experience

### Seamless Operation
- **Transparent Fallbacks**: Users unaware of service switches
- **Consistent Interface**: Same API regardless of backend service
- **Quality Maintenance**: Maintain response quality across services
- **Performance Optimization**: Minimize latency during transitions

### User Notifications
```python
NOTIFICATION_TYPES = {
    "fallback_activated": {
        "message": "Switched to backup service for better performance",
        "level": "INFO",
        "show_to_user": False
    },
    "service_restored": {
        "message": "Primary service restored",
        "level": "INFO", 
        "show_to_user": False
    },
    "configuration_required": {
        "message": "External API configuration needed",
        "level": "WARNING",
        "show_to_user": True
    }
}
```

## Monitoring and Analytics

### Fallback Metrics
- **Fallback Frequency**: How often fallbacks occur
- **Service Reliability**: Uptime and success rates per service
- **Transition Time**: Time taken for service switches
- **User Impact**: Service quality during fallbacks

### Performance Tracking
```python
class FallbackMetrics:
    total_fallbacks: int
    fallback_reasons: Dict[str, int]
    service_uptime: Dict[str, float]
    average_transition_time: float
    user_satisfaction_score: float
```

## Integration Points

### Server Integration
- Integrate with `stt_server.py` for service management
- Support WebSocket communication for status updates
- Provide APIs for manual service switching

### Configuration Integration
- Command-line arguments for fallback preferences
- Configuration file support for complex setups
- Environment variable overrides for credentials
- Runtime configuration updates

## Testing Strategy

### Fallback Testing
- **Chaos Engineering**: Intentional service failures
- **Load Testing**: Performance under stress
- **Network Simulation**: Various network conditions
- **Configuration Testing**: Different configuration scenarios

### Validation
- **Service Compatibility**: Ensure all services work correctly
- **Transition Smoothness**: Validate seamless transitions
- **Error Recovery**: Test error handling and recovery
- **User Experience**: Validate user experience quality

## Security Considerations

### Credential Management
- Secure storage of API keys and credentials
- Rotation and expiration handling
- Environment-based configuration
- Audit logging for credential access

### Data Privacy
- Ensure appropriate data handling per service
- Local vs. external data processing awareness
- Compliance with privacy regulations
- User consent for external service usage

This design ensures robust, reliable fallback capabilities that maintain service quality and user experience even when primary local services are unavailable.
