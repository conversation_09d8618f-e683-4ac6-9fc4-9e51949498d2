# Configuration Schema Design for Ollama Integration

## Overview
Design for comprehensive configuration schema that integrates Ollama settings into the existing RealtimeSTT server architecture. The schema maintains backward compatibility while adding new Ollama-specific parameters and fallback configurations.

## Command Line Arguments

### New Ollama Arguments
```python
# Add to parse_arguments() function in stt_server.py

# Ollama Integration Arguments
parser.add_argument('--use_ollama', action='store_true', default=False,
                    help='Enable Ollama backend for local model processing. When enabled, the system will use local Ollama models instead of external APIs. Default is False.')

parser.add_argument('--ollama_model', type=str, default='gemma3:1b',
                    help='Specify the Ollama model to use for processing. Options include gemma3:1b, gemma:2b, llama3.2:1b, or any available Ollama model. Default is gemma3:1b.')

parser.add_argument('--ollama_host', type=str, default='localhost:11434',
                    help='Ollama server host and port. Specify the address where Ollama server is running. Default is localhost:11434.')

parser.add_argument('--ollama_timeout', type=float, default=30.0,
                    help='Timeout in seconds for Ollama requests. Adjust based on model size and system performance. Default is 30.0 seconds.')

parser.add_argument('--ollama_max_retries', type=int, default=3,
                    help='Maximum number of retry attempts for failed Ollama requests. Default is 3.')

parser.add_argument('--ollama_auto_download', action='store_true', default=True,
                    help='Automatically download Ollama models if not available locally. Default is True.')

# Fallback Configuration Arguments
parser.add_argument('--enable_fallback', action='store_true', default=True,
                    help='Enable automatic fallback to external APIs when Ollama is unavailable. Default is True.')

parser.add_argument('--fallback_chain', type=str, default='ollama,openai,azure',
                    help='Comma-separated list of services to try in order. Options: ollama, openai, azure, elevenlabs. Default is ollama,openai,azure.')

parser.add_argument('--fallback_timeout', type=float, default=60.0,
                    help='Time in seconds before attempting to switch back to primary service. Default is 60.0 seconds.')

# Health Monitoring Arguments
parser.add_argument('--health_check_interval', type=float, default=30.0,
                    help='Interval in seconds between health checks for all services. Default is 30.0 seconds.')

parser.add_argument('--performance_threshold', type=float, default=5.0,
                    help='Maximum response time in seconds before triggering fallback. Default is 5.0 seconds.')
```

## Recorder Configuration Updates

### Extended recorder_config Dictionary
```python
# Add to recorder_config in main_async() function

recorder_config.update({
    # Ollama Configuration
    'use_ollama': args.use_ollama,
    'ollama_model': args.ollama_model,
    'ollama_host': args.ollama_host,
    'ollama_timeout': args.ollama_timeout,
    'ollama_max_retries': args.ollama_max_retries,
    'ollama_auto_download': args.ollama_auto_download,
    
    # Fallback Configuration
    'enable_fallback': args.enable_fallback,
    'fallback_chain': args.fallback_chain.split(','),
    'fallback_timeout': args.fallback_timeout,
    
    # Health Monitoring
    'health_check_interval': args.health_check_interval,
    'performance_threshold': args.performance_threshold,
    
    # Service Credentials (from environment variables)
    'openai_api_key': os.environ.get('OPENAI_API_KEY'),
    'azure_speech_key': os.environ.get('AZURE_SPEECH_KEY'),
    'azure_speech_region': os.environ.get('AZURE_SPEECH_REGION'),
    'elevenlabs_api_key': os.environ.get('ELEVENLABS_API_KEY'),
})
```

## Configuration File Schema

### JSON Configuration File (config.json)
```json
{
  "ollama": {
    "enabled": true,
    "model": "gemma3:1b",
    "host": "localhost:11434",
    "timeout": 30.0,
    "max_retries": 3,
    "auto_download": true,
    "models": {
      "primary": "gemma3:1b",
      "fallback": ["gemma:2b", "llama3.2:1b"]
    }
  },
  "fallback": {
    "enabled": true,
    "chain": ["ollama", "openai", "azure"],
    "timeout": 60.0,
    "retry_intervals": [10, 30, 60, 300]
  },
  "health_monitoring": {
    "enabled": true,
    "check_interval": 30.0,
    "performance_threshold": 5.0,
    "quality_threshold": 0.95
  },
  "external_apis": {
    "openai": {
      "model": "gpt-3.5-turbo",
      "max_tokens": 1000,
      "temperature": 0.0
    },
    "azure": {
      "voice": "en-US-SoniaNeural",
      "rate": 24,
      "pitch": 10
    },
    "elevenlabs": {
      "model": "eleven_monolingual_v1",
      "voice_id": "default"
    }
  },
  "logging": {
    "level": "INFO",
    "log_fallbacks": true,
    "log_performance": true
  }
}
```

### YAML Configuration File (config.yaml)
```yaml
ollama:
  enabled: true
  model: "gemma3:1b"
  host: "localhost:11434"
  timeout: 30.0
  max_retries: 3
  auto_download: true
  models:
    primary: "gemma3:1b"
    fallback:
      - "gemma:2b"
      - "llama3.2:1b"

fallback:
  enabled: true
  chain:
    - "ollama"
    - "openai"
    - "azure"
  timeout: 60.0
  retry_intervals: [10, 30, 60, 300]

health_monitoring:
  enabled: true
  check_interval: 30.0
  performance_threshold: 5.0
  quality_threshold: 0.95

external_apis:
  openai:
    model: "gpt-3.5-turbo"
    max_tokens: 1000
    temperature: 0.0
  azure:
    voice: "en-US-SoniaNeural"
    rate: 24
    pitch: 10
  elevenlabs:
    model: "eleven_monolingual_v1"
    voice_id: "default"

logging:
  level: "INFO"
  log_fallbacks: true
  log_performance: true
```

## Environment Variables

### Ollama Configuration
```bash
# Ollama Settings
REALTIMESTT_USE_OLLAMA=true
REALTIMESTT_OLLAMA_MODEL=gemma3:1b
REALTIMESTT_OLLAMA_HOST=localhost:11434
REALTIMESTT_OLLAMA_TIMEOUT=30.0

# External API Keys
OPENAI_API_KEY=your_openai_api_key_here
AZURE_SPEECH_KEY=your_azure_speech_key_here
AZURE_SPEECH_REGION=your_azure_region_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Fallback Configuration
REALTIMESTT_ENABLE_FALLBACK=true
REALTIMESTT_FALLBACK_CHAIN=ollama,openai,azure
REALTIMESTT_FALLBACK_TIMEOUT=60.0

# Health Monitoring
REALTIMESTT_HEALTH_CHECK_INTERVAL=30.0
REALTIMESTT_PERFORMANCE_THRESHOLD=5.0
```

## Configuration Validation

### Validation Schema
```python
CONFIG_VALIDATION_SCHEMA = {
    "ollama": {
        "enabled": {"type": "boolean", "default": False},
        "model": {"type": "string", "default": "gemma3:1b"},
        "host": {"type": "string", "pattern": r"^[\w\.-]+:\d+$"},
        "timeout": {"type": "number", "minimum": 1.0, "maximum": 300.0},
        "max_retries": {"type": "integer", "minimum": 0, "maximum": 10}
    },
    "fallback": {
        "enabled": {"type": "boolean", "default": True},
        "chain": {"type": "array", "items": {"enum": ["ollama", "openai", "azure", "elevenlabs"]}},
        "timeout": {"type": "number", "minimum": 10.0, "maximum": 3600.0}
    },
    "health_monitoring": {
        "enabled": {"type": "boolean", "default": True},
        "check_interval": {"type": "number", "minimum": 5.0, "maximum": 300.0},
        "performance_threshold": {"type": "number", "minimum": 1.0, "maximum": 60.0}
    }
}
```

### Configuration Loader
```python
class ConfigurationLoader:
    """
    Loads and validates configuration from multiple sources.
    Priority: CLI args > Environment variables > Config file > Defaults
    """
    
    def load_config(self, config_file=None, cli_args=None):
        config = self.load_defaults()
        
        if config_file:
            file_config = self.load_config_file(config_file)
            config = self.merge_configs(config, file_config)
        
        env_config = self.load_environment_config()
        config = self.merge_configs(config, env_config)
        
        if cli_args:
            config = self.merge_configs(config, cli_args)
        
        self.validate_config(config)
        return config
```

## WebSocket Message Extensions

### New Message Types
```python
# Ollama Status Messages
OLLAMA_STATUS_MESSAGE = {
    "type": "ollama_status",
    "status": "connected|disconnected|error",
    "model": "gemma3:1b",
    "response_time": 1.23,
    "timestamp": "2024-01-01T12:00:00Z"
}

# Fallback Notification Messages
FALLBACK_MESSAGE = {
    "type": "service_fallback",
    "from_service": "ollama",
    "to_service": "openai",
    "reason": "timeout|error|unavailable",
    "timestamp": "2024-01-01T12:00:00Z"
}

# Health Check Messages
HEALTH_CHECK_MESSAGE = {
    "type": "health_check",
    "services": {
        "ollama": {"status": "healthy", "response_time": 1.23},
        "openai": {"status": "healthy", "response_time": 2.45},
        "azure": {"status": "unavailable", "error": "API key missing"}
    },
    "timestamp": "2024-01-01T12:00:00Z"
}
```

## Configuration Migration

### Migration Strategy
```python
class ConfigurationMigrator:
    """
    Handles migration from older configuration formats.
    Ensures backward compatibility during upgrades.
    """
    
    def migrate_config(self, old_config, target_version):
        migrations = {
            "1.0": self.migrate_to_v1_0,
            "2.0": self.migrate_to_v2_0
        }
        
        current_version = old_config.get("version", "0.9")
        
        for version, migration_func in migrations.items():
            if self.version_compare(current_version, version) < 0:
                old_config = migration_func(old_config)
                current_version = version
        
        return old_config
```

## Default Configuration

### Complete Default Configuration
```python
DEFAULT_CONFIG = {
    "version": "2.0",
    "ollama": {
        "enabled": False,
        "model": "gemma3:1b",
        "host": "localhost:11434",
        "timeout": 30.0,
        "max_retries": 3,
        "auto_download": True
    },
    "fallback": {
        "enabled": True,
        "chain": ["ollama", "openai", "azure"],
        "timeout": 60.0,
        "retry_intervals": [10, 30, 60, 300]
    },
    "health_monitoring": {
        "enabled": True,
        "check_interval": 30.0,
        "performance_threshold": 5.0,
        "quality_threshold": 0.95
    },
    "logging": {
        "level": "INFO",
        "log_fallbacks": True,
        "log_performance": True
    }
}
```

This configuration schema provides a comprehensive, flexible, and backward-compatible approach to integrating Ollama settings into the RealtimeSTT server while maintaining the existing functionality and user experience.
